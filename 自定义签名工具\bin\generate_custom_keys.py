#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义证书和私钥生成器
By.举个🌰 2025
"""

import os
import subprocess
import sys
from datetime import datetime, <PERSON><PERSON><PERSON>

def generate_custom_certificate():
    """生成自定义证书和私钥"""
    print("=" * 60)
    print("自定义证书生成器 - By.举个🌰 2025")
    print("=" * 60)
    
    # 获取用户输入
    print("请输入证书信息 (直接回车使用默认值):")
    
    country = input("国家代码 (默认: CN): ").strip() or "CN"
    state = input("省份 (默认: Beijing): ").strip() or "Beijing"
    city = input("城市 (默认: Beijing): ").strip() or "Beijing"
    org = input("组织名称 (默认: CustomROM): ").strip() or "CustomROM"
    org_unit = input("组织单位 (默认: Development): ").strip() or "Development"
    common_name = input("通用名称 (默认: CustomROM Signer): ").strip() or "CustomROM Signer"
    email = input("邮箱 (默认: <EMAIL>): ").strip() or "<EMAIL>"
    
    # 证书有效期
    try:
        validity_days = int(input("证书有效期(天数，默认: 7300): ").strip() or "7300")
    except ValueError:
        validity_days = 7300
    
    # 创建证书主题
    subject = f"/C={country}/ST={state}/L={city}/O={org}/OU={org_unit}/CN={common_name}/emailAddress={email}"
    
    print(f"\n生成证书，主题: {subject}")
    print(f"有效期: {validity_days} 天")
    
    # 生成私钥
    print("\n1. 生成RSA私钥...")
    private_key_cmd = [
        "openssl", "genrsa", "-out", "custom_key.pem", "2048"
    ]
    
    try:
        subprocess.run(private_key_cmd, check=True, capture_output=True)
        print("   私钥生成成功: custom_key.pem")
    except subprocess.CalledProcessError as e:
        print(f"   私钥生成失败: {e}")
        return False
    except FileNotFoundError:
        print("   错误: 未找到 openssl 命令，请确保已安装 OpenSSL")
        return False
    
    # 生成证书
    print("\n2. 生成X.509证书...")
    cert_cmd = [
        "openssl", "req", "-new", "-x509", "-key", "custom_key.pem",
        "-out", "custom_key.x509.pem", "-days", str(validity_days),
        "-subj", subject
    ]
    
    try:
        subprocess.run(cert_cmd, check=True, capture_output=True)
        print("   证书生成成功: custom_key.x509.pem")
    except subprocess.CalledProcessError as e:
        print(f"   证书生成失败: {e}")
        return False
    
    # 转换私钥为PKCS#8格式
    print("\n3. 转换私钥为PKCS#8格式...")
    pkcs8_cmd = [
        "openssl", "pkcs8", "-topk8", "-outform", "DER",
        "-in", "custom_key.pem", "-out", "custom_key.pk8", "-nocrypt"
    ]
    
    try:
        subprocess.run(pkcs8_cmd, check=True, capture_output=True)
        print("   PKCS#8私钥生成成功: custom_key.pk8")
    except subprocess.CalledProcessError as e:
        print(f"   PKCS#8私钥生成失败: {e}")
        return False
    
    # 清理临时文件
    try:
        os.remove("custom_key.pem")
        print("   清理临时文件完成")
    except:
        pass
    
    # 生成证书信息文件
    cert_info = f"""
自定义证书信息 - By.举个🌰 2025
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
========================================

证书主题: {subject}
有效期: {validity_days} 天
证书文件: custom_key.x509.pem
私钥文件: custom_key.pk8

注意: 请妥善保管这些文件，它们用于ROM签名！
"""
    
    with open("custom_cert_info.txt", "w", encoding="utf-8") as f:
        f.write(cert_info)
    
    print("\n" + "=" * 60)
    print("自定义证书生成完成！")
    print("生成的文件:")
    print("  - custom_key.x509.pem (证书文件)")
    print("  - custom_key.pk8 (私钥文件)")
    print("  - custom_cert_info.txt (证书信息)")
    print("=" * 60)
    
    return True

def main():
    try:
        if not generate_custom_certificate():
            print("证书生成失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n操作已取消")
        sys.exit(0)
    except Exception as e:
        print(f"\n发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
