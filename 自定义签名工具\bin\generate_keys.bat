@echo off
title 生成自定义证书 - By.举个🌰 2025
echo ========================================
echo 自定义证书生成工具 - By.举个🌰 2025
echo ========================================
echo.

REM 检查是否存在openssl
where openssl >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 OpenSSL 工具！
    echo 请安装 OpenSSL 或使用 Git Bash 中的 openssl
    echo.
    pause
    exit /b 1
)

echo 正在生成自定义证书和私钥...
echo.

REM 生成私钥
echo 1. 生成RSA私钥...
openssl genrsa -out custom_key.pem 2048
if %errorlevel% neq 0 (
    echo 私钥生成失败！
    pause
    exit /b 1
)
echo    私钥生成成功: custom_key.pem

REM 生成证书
echo.
echo 2. 生成X.509证书...
openssl req -new -x509 -key custom_key.pem -out custom_key.x509.pem -days 7300 -subj "/C=CN/ST=Beijing/L=Beijing/O=CustomROM/OU=Development/CN=CustomROM Signer/emailAddress=<EMAIL>"
if %errorlevel% neq 0 (
    echo 证书生成失败！
    pause
    exit /b 1
)
echo    证书生成成功: custom_key.x509.pem

REM 转换私钥为PKCS#8格式
echo.
echo 3. 转换私钥为PKCS#8格式...
openssl pkcs8 -topk8 -outform DER -in custom_key.pem -out custom_key.pk8 -nocrypt
if %errorlevel% neq 0 (
    echo PKCS#8私钥生成失败！
    pause
    exit /b 1
)
echo    PKCS#8私钥生成成功: custom_key.pk8

REM 清理临时文件
del custom_key.pem >nul 2>nul

REM 生成证书信息文件
echo 自定义证书信息 - By.举个🌰 2025 > custom_cert_info.txt
echo 生成时间: %date% %time% >> custom_cert_info.txt
echo ======================================== >> custom_cert_info.txt
echo. >> custom_cert_info.txt
echo 证书主题: /C=CN/ST=Beijing/L=Beijing/O=CustomROM/OU=Development/CN=CustomROM Signer/emailAddress=<EMAIL> >> custom_cert_info.txt
echo 有效期: 7300 天 >> custom_cert_info.txt
echo 证书文件: custom_key.x509.pem >> custom_cert_info.txt
echo 私钥文件: custom_key.pk8 >> custom_cert_info.txt
echo. >> custom_cert_info.txt
echo 注意: 请妥善保管这些文件，它们用于ROM签名！ >> custom_cert_info.txt

echo.
echo ========================================
echo 自定义证书生成完成！
echo 生成的文件:
echo   - custom_key.x509.pem (证书文件)
echo   - custom_key.pk8 (私钥文件)  
echo   - custom_cert_info.txt (证书信息)
echo ========================================
echo.
pause
