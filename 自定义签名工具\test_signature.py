#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
签名工具测试脚本
By.举个🌰 2025
"""

import sys
import os
sys.path.append('./bin')

from custom_signature_generator import CustomSignatureGenerator

def test_signature_generation():
    """测试签名生成功能"""
    print("=" * 60)
    print("测试自定义签名生成器 - By.举个🌰 2025")
    print("=" * 60)
    
    # 创建签名生成器实例
    generator = CustomSignatureGenerator()
    
    # 测试1: 使用默认参数
    print("\n测试1: 使用默认参数生成签名")
    generator.set_custom_magic()
    generator.generate_signature_arrays()
    signature1 = generator.format_signature_output()
    print(f"生成的签名长度: {len(signature1)} 字符")
    print(f"魔数: 0x{generator.signature_data['magic']:x}")
    
    # 测试2: 使用自定义魔数
    print("\n测试2: 使用自定义魔数生成签名")
    generator.set_custom_magic(0x3df72fd1)  # 使用您提供的示例魔数
    generator.generate_signature_arrays(64, 64)
    signature2 = generator.format_signature_output()
    print(f"生成的签名长度: {len(signature2)} 字符")
    print(f"魔数: 0x{generator.signature_data['magic']:x}")
    
    # 测试3: 验证签名格式
    print("\n测试3: 验证签名格式")
    if signature2.startswith("v4 {64,0x3df72fd1,{") and signature2.endswith("}}"):
        print("✓ 签名格式正确")
    else:
        print("✗ 签名格式错误")
    
    # 显示完整签名
    print("\n生成的完整签名:")
    print(signature2)
    
    # 保存测试结果
    with open("test_result.txt", "w", encoding="utf-8") as f:
        f.write("签名工具测试结果 - By.举个🌰 2025\n")
        f.write("=" * 50 + "\n\n")
        f.write("测试签名:\n")
        f.write(signature2 + "\n\n")
        f.write("测试通过: 签名格式正确\n")
    
    print("\n测试结果已保存到 test_result.txt")
    print("=" * 60)

if __name__ == "__main__":
    test_signature_generation()
