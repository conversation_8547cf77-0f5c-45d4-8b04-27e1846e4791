================================================================
自定义ROM签名工具 - 使用说明
By.举个🌰 2025
================================================================

一、工具简介
============
这是一个基于您原有签名工具原理开发的自定义ROM签名工具。
主要功能：
1. ROM固件解包和重新打包
2. 自定义签名生成（v4格式）
3. 自定义证书和私钥生成
4. 完全兼容原有工具的操作流程

二、文件结构
============
自定义签名工具/
├── ROM_Custom_Sign.bat          # 主程序（英文界面）
├── ROM_Custom_Sign_CN.bat       # 主程序（中文界面）
├── 使用说明.txt                 # 本文件
├── README.md                    # 详细说明文档
├── bin/                         # 工具目录
│   ├── 7za.exe                 # 压缩解压工具
│   ├── signapk.jar             # Android签名工具
│   ├── custom_signature_generator.py  # 签名生成器
│   ├── generate_custom_keys.py # 证书生成器
│   ├── generate_keys.bat       # 证书生成批处理
│   ├── custom_key.x509.pem     # 自定义证书
│   └── custom_key.pk8          # 自定义私钥

三、使用步骤
============

步骤1：启动工具
--------------
双击运行 ROM_Custom_Sign.bat (英文界面)
或者运行 ROM_Custom_Sign_CN.bat (中文界面，可能有编码问题)

步骤2：解包固件
--------------
1. 将ROM固件重命名为 update.zip 放在工具根目录
2. 在主菜单选择 "1" 进行解包
3. 解包后的文件在 update 目录中

步骤3：修改固件（可选）
--------------------
在 update 目录中手动修改固件文件
- 可以替换系统应用
- 修改配置文件
- 添加自定义内容

步骤4：生成自定义签名
------------------
1. 在主菜单选择 "3" 生成自定义签名
2. 选择是否使用自定义魔数
3. 设置签名数组大小（默认64）
4. 签名信息保存在 bin/current_signature.txt

步骤5：查看签名信息
----------------
在主菜单选择 "4" 查看当前生成的签名信息

步骤6：重新打包签名
----------------
1. 在主菜单选择 "2" 进行打包和签名
2. 生成的 update_signed.zip 即为最终固件

四、签名格式说明
===============
生成的签名格式为：
v4 {64,0x[魔数],[数组1],[数组2]}

示例：
v4 {64,0x3df72fd1,{2939582159,1594980634,...},{3475861637,2896311143,...}}

其中：
- v4: 签名版本
- 64: 头部大小
- 0x3df72fd1: 魔数（可自定义）
- 两个数组: 各包含64个32位无符号整数

五、高级功能
============

生成自定义证书：
--------------
方法1（推荐）：
cd bin
python generate_custom_keys.py

方法2：
cd bin
generate_keys.bat

注意：需要安装OpenSSL

六、注意事项
============
1. 请妥善保管生成的证书和私钥文件
2. 确保修改的固件与目标设备兼容
3. 刷机前请备份原始固件
4. 刷机有风险，操作需谨慎

七、测试验证
============
运行测试脚本验证工具是否正常：
python test_signature.py

八、技术支持
============
如遇问题，请检查：
1. Python环境是否正确安装
2. 证书文件是否存在
3. 固件文件是否正确放置

================================================================
版权信息：By.举个🌰 2025
================================================================
