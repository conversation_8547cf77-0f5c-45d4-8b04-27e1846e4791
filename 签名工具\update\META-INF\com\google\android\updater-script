getprop("ro.product.device") == "p291_iptv" || abort("E3004: This package is for \"p291_iptv\" devices; this is a \"" + getprop("ro.product.device") + "\".");
if ota_zip_check() == "1" then
write_dtb_image(package_extract_file("dt.img"));
set_bootloader_env("upgrade_step", "3");
backup_update_package("/dev/block/mmcblk0", "1894");
ui_print("update bootloader.img...");
write_bootloader_image(package_extract_file("bootloader.img"));
set_bootloader_env("upgrade_step", "2");
set_bootloader_env("upgrade_step", "3");
reboot_recovery();
else
ui_print("Target: AndroidP/CMDC/S905L3:9.0.0/CM311-1-ALL/V.686.03:eng/test-keys");
set_bootloader_env("upgrade_step", "3");
show_progress(0.650000, 0);
ui_print("Patching system image unconditionally...");
block_image_update("/dev/block/system", package_extract_file("system.transfer.list"), "system.new.dat.br", "system.patch.dat") ||
  abort("E1001: Failed to update system image.");
show_progress(0.100000, 0);
ui_print("Patching vendor image unconditionally...");
block_image_update("/dev/block/vendor", package_extract_file("vendor.transfer.list"), "vendor.new.dat.br", "vendor.patch.dat") ||
  abort("E2001: Failed to update vendor image.");
show_progress(0.050000, 5);
package_extract_file("boot.img", "/dev/block/boot");
show_progress(0.200000, 10);
ui_print("Patching odm image unconditionally...");
block_image_update("/dev/block/odm", package_extract_file("odm.transfer.list"), "odm.new.dat.br", "odm.patch.dat") ||
  abort("E2001: Failed to update odm image.");
ui_print("Patching product image unconditionally...");
block_image_update("/dev/block/product", package_extract_file("product.transfer.list"), "product.new.dat.br", "product.patch.dat") ||
  abort("E2001: Failed to update product image.");
ui_print("update vbmeta.img...");
package_extract_file("vbmeta.img", "/dev/block/vbmeta");
ui_print("update logo.img...");
package_extract_file("logo.img", "/dev/block/logo");
ui_print("update dtb.img...");
write_dtb_image(package_extract_file("dt.img"));
ui_print("update dtbo.img...");
package_extract_file("dtbo.img", "/dev/block/dtbo");
if get_chip_type() == "0" then
ui_print("update p291-bootloader.img...");
write_bootloader_image(package_extract_file("p291-bootloader.img"));
else
ui_print("update bootloader.img...");
write_bootloader_image(package_extract_file("bootloader.img"));
if get_chip_type() == "1" then
wipe_ddr_parameter();
endif;
endif;
if get_update_stage() == "2" then
format("ext4", "EMMC", "/dev/block/metadata", "0", "/metadata");
format("ext4", "EMMC", "/dev/block/tee", "0", "/tee");
wipe_cache();
set_update_stage("0");
endif;
set_bootloader_env("upgrade_step", "2");
set_bootloader_env("force_auto_update", "false");
set_bootloader_env("display_layer", "osd1");
endif;
set_progress(1.000000);
