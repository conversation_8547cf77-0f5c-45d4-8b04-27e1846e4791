#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义签名生成器
By.举个🌰 2025
"""

import random
import os
import sys
from datetime import datetime

class CustomSignatureGenerator:
    def __init__(self):
        self.signature_data = {
            'version': 'v4',
            'header_size': 64,
            'magic': None,
            'array1': [],
            'array2': []
        }
    
    def generate_random_uint32(self):
        """生成随机的32位无符号整数"""
        return random.randint(0, 4294967295)
    
    def generate_signature_arrays(self, array1_size=64, array2_size=64):
        """生成签名数组"""
        self.signature_data['array1'] = [self.generate_random_uint32() for _ in range(array1_size)]
        self.signature_data['array2'] = [self.generate_random_uint32() for _ in range(array2_size)]
    
    def set_custom_magic(self, magic=None):
        """设置自定义魔数"""
        if magic is None:
            self.signature_data['magic'] = self.generate_random_uint32()
        else:
            self.signature_data['magic'] = magic
    
    def format_signature_output(self):
        """格式化签名输出"""
        array1_str = ','.join(map(str, self.signature_data['array1']))
        array2_str = ','.join(map(str, self.signature_data['array2']))
        
        signature_str = f"{self.signature_data['version']} {{{self.signature_data['header_size']},0x{self.signature_data['magic']:x},{{{array1_str}}},{{{array2_str}}}}}"
        
        return signature_str
    
    def save_signature_to_file(self, filename='current_signature.txt'):
        """保存签名到文件"""
        signature_str = self.format_signature_output()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("自定义ROM签名信息 - By.举个🌰 2025\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")
            f.write("签名格式:\n")
            f.write(signature_str + "\n\n")
            f.write("签名详细信息:\n")
            f.write(f"版本: {self.signature_data['version']}\n")
            f.write(f"头部大小: {self.signature_data['header_size']}\n")
            f.write(f"魔数: 0x{self.signature_data['magic']:x} ({self.signature_data['magic']})\n")
            f.write(f"数组1长度: {len(self.signature_data['array1'])}\n")
            f.write(f"数组2长度: {len(self.signature_data['array2'])}\n")
            f.write("\n" + "=" * 80 + "\n")
        
        return signature_str
    
    def interactive_generate(self):
        """交互式生成签名"""
        print("=" * 60)
        print("自定义签名生成器 - By.举个🌰 2025")
        print("=" * 60)
        
        # 询问是否使用自定义魔数
        use_custom_magic = input("是否使用自定义魔数? (y/n, 默认n): ").lower().strip()
        
        if use_custom_magic == 'y':
            try:
                magic_input = input("请输入魔数 (十进制或0x开头的十六进制): ").strip()
                if magic_input.startswith('0x'):
                    magic = int(magic_input, 16)
                else:
                    magic = int(magic_input)
                self.set_custom_magic(magic)
                print(f"使用自定义魔数: 0x{magic:x}")
            except ValueError:
                print("输入格式错误，使用随机魔数")
                self.set_custom_magic()
        else:
            self.set_custom_magic()
            print(f"使用随机魔数: 0x{self.signature_data['magic']:x}")
        
        # 询问数组大小
        try:
            array_size = input("请输入签名数组大小 (默认64): ").strip()
            if array_size:
                array_size = int(array_size)
            else:
                array_size = 64
        except ValueError:
            array_size = 64
            print("输入格式错误，使用默认大小64")
        
        print(f"生成 {array_size} 个元素的签名数组...")
        
        # 生成签名
        self.generate_signature_arrays(array_size, array_size)
        
        # 保存并显示结果
        signature_str = self.save_signature_to_file()
        
        print("\n" + "=" * 60)
        print("签名生成完成！")
        print("=" * 60)
        print("生成的签名:")
        print(signature_str)
        print("\n签名信息已保存到 current_signature.txt")
        print("=" * 60)

def main():
    try:
        generator = CustomSignatureGenerator()
        generator.interactive_generate()
    except KeyboardInterrupt:
        print("\n\n操作已取消")
        sys.exit(0)
    except Exception as e:
        print(f"\n发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
