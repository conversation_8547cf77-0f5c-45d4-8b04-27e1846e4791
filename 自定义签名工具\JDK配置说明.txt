================================================================
JDK配置说明 - ROM自定义签名工具
By.举个🌰 2025
================================================================

一、JDK配置
===========
本工具已自动配置使用项目中的JDK 1.8.0_461，无需额外安装Java环境。

JDK路径：
../jdk1.8.0_461/bin/java.exe

二、JDK版本信息
===============
Java版本：1.8.0_461
Java运行环境：Java(TM) SE Runtime Environment (build 1.8.0_461-b11)
Java虚拟机：Java HotSpot(TM) 64-Bit Server VM (build 25.461-b11, mixed mode)

三、工具配置
============
以下工具已配置使用本地JDK：

1. Python GUI工具 (ROM_Signature_Tool.py)
   - 自动检测JDK路径
   - 使用绝对路径调用java.exe
   - 在日志中显示JDK状态

2. 批处理工具 (ROM_Custom_Sign.bat)
   - 使用相对路径：..\..\jdk1.8.0_461\bin\java.exe
   - 适用于命令行环境

3. 中文批处理工具 (ROM_Custom_Sign_CN.bat)
   - 同样使用本地JDK路径
   - 中文界面版本

四、验证JDK
===========
可以通过以下命令验证JDK是否正常工作：

在PowerShell中：
& "E:\ROM\MIK-4.3\311-1E软件逆向\311-1E软件逆向\jdk1.8.0_461\bin\java.exe" -version

在命令提示符中：
"E:\ROM\MIK-4.3\311-1E软件逆向\311-1E软件逆向\jdk1.8.0_461\bin\java.exe" -version

五、优势
========
1. 无需安装Java：使用项目自带的JDK
2. 版本固定：避免Java版本兼容性问题
3. 便携性好：整个项目可以直接复制使用
4. 稳定可靠：JDK 1.8是Android签名的标准版本

六、故障排除
============
如果遇到JDK相关问题：

1. 检查JDK目录是否完整
   - 确保jdk1.8.0_461目录存在
   - 确保bin/java.exe文件存在

2. 检查路径权限
   - 确保有读取和执行权限

3. 检查系统兼容性
   - JDK 1.8.0_461为64位版本
   - 需要64位Windows系统

================================================================
版权信息：By.举个🌰 2025
================================================================
