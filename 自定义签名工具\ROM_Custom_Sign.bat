@echo off
chcp 65001 >nul
color 0a
title Custom ROM Signature Tool - By.JuGeGeLi 2025
:menu
cls
echo ################################################################
echo # Custom ROM Signature Tool - By.JuGeGeLi 2025                #
echo # Please select the operation you need                        #
echo # 1 -- Unpack      (Extract firmware for manual modification) #
echo # 2 -- Pack+Sign   (Repack and sign modified firmware)       #
echo # 3 -- Generate Custom Signature                              #
echo # 4 -- View Current Signature Info                            #
echo # 5 -- Exit                                                   #
echo ################################################################
:cho
set choice=
set /p choice= Please enter your choice (number):
if /i "%choice%"=="1" goto unpack
if /i "%choice%"=="2" goto pack
if /i "%choice%"=="3" goto generate_signature
if /i "%choice%"=="4" goto view_signature
if /i "%choice%"=="5" goto end
goto menu

:unpack
if not exist .\update.zip goto nofile
echo.
echo ###################################
echo #   Starting firmware unpack...  #
echo ###################################
echo.
if exist .\update rd /S /Q .\update
md .\update
cd .\update
..\bin\7za.exe x -y ..\update.zip
cd ..
echo.
echo ########################################################
echo #   Firmware unpacked successfully...                 #
echo #   The update directory contains unpacked firmware   #
echo ########################################################
echo.
pause
goto menu

:pack
if not exist .\update goto nodate
echo.
echo ####################################
echo #   Starting pack+sign...         #
echo ####################################
echo.
cd .\update
..\bin\7za.exe a -y ..\update_diy.zip
cd ..
echo.
echo Starting signature...
cd .\bin
java.exe -Xmx2048m -jar signapk.jar -w custom_key.x509.pem custom_key.pk8 ..\update_diy.zip ..\update_signed.zip
del ..\update_diy.zip
cd ..
echo Signature completed...
echo.
echo #########################################################
echo #   Firmware pack+sign completed...                    #
echo #   Generated update_signed.zip is ready for flashing  #
echo #########################################################
echo.
pause
goto menu

:generate_signature
echo.
echo ####################################
echo #   Generating custom signature...#
echo ####################################
echo.
cd .\bin
python custom_signature_generator.py
cd ..
echo.
echo Custom signature generated successfully!
pause
goto menu

:view_signature
echo.
echo ####################################
echo #   View current signature info...#
echo ####################################
echo.
if exist .\bin\current_signature.txt (
    type .\bin\current_signature.txt
) else (
    echo Signature info file not found, please generate custom signature first!
)
echo.
pause
goto menu

:nofile
echo.
echo #########################################################
echo #   No update.zip firmware package found               #
echo #   Please download firmware and rename to update.zip  #
echo #########################################################
echo.
pause
goto menu

:nodate
echo.
echo #########################################
echo #   No unpacked firmware, unpack first!#
echo #########################################
echo.
pause
goto menu

:end
exit
