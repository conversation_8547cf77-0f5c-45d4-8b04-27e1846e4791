# 自定义ROM签名工具

**By.举个🌰 2025**

## 项目简介

这是一个自定义ROM签名工具，基于原有签名工具的原理，但增加了自定义签名功能。该工具可以：

- 解包ROM固件
- 重新打包并签名ROM固件  
- 生成自定义签名格式（v4格式）
- 生成自定义证书和私钥

## 功能特性

### 1. 固件操作
- **解包**: 解压ROM固件包，方便手动修改
- **打包+签名**: 重新打包修改后的固件并进行签名

### 2. 自定义签名
- 生成v4格式的自定义签名
- 支持自定义魔数
- 支持自定义数组大小
- 签名格式: `v4 {64,0x3df72fd1,{array1},{array2}}`

### 3. 证书管理
- 生成自定义证书和私钥
- 支持自定义证书信息
- 自动转换为Android签名所需格式

## 使用方法

### 准备工作

1. 确保系统已安装Python 3.6+
2. 如需生成自定义证书，请安装OpenSSL

### 基本使用

1. **启动工具**
   ```
   双击运行 ROM_Custom_Sign.bat
   ```

2. **解包固件**
   - 将ROM固件重命名为 `update.zip` 放在工具目录下
   - 选择选项 `1` 进行解包
   - 解包后的文件在 `update` 目录中

3. **修改固件**
   - 在 `update` 目录中手动修改固件文件
   - 可以替换系统应用、修改配置等

4. **重新打包签名**
   - 选择选项 `2` 进行打包和签名
   - 生成的 `update_signed.zip` 即为签名后的固件

5. **生成自定义签名**
   - 选择选项 `3` 生成自定义签名
   - 可以设置自定义魔数和数组大小
   - 签名信息保存在 `bin/current_signature.txt`

6. **查看签名信息**
   - 选择选项 `4` 查看当前签名信息

### 高级功能

#### 生成自定义证书

在 `bin` 目录下运行：

**使用Python脚本（推荐）:**
```bash
python generate_custom_keys.py
```

**使用批处理文件:**
```bash
generate_keys.bat
```

#### 自定义签名格式

生成的签名格式为：
```
v4 {64,0x[魔数],[数组1],[数组2]}
```

示例：
```
v4 {64,0x3df72fd1,{2939582159,1594980634,...},{3475861637,2896311143,...}}
```

## 文件结构

```
自定义签名工具/
├── ROM_Custom_Sign.bat          # 主程序
├── README.md                    # 说明文档
├── bin/                         # 工具目录
│   ├── 7za.exe                 # 压缩解压工具
│   ├── signapk.jar             # Android签名工具
│   ├── custom_signature_generator.py  # 签名生成器
│   ├── generate_custom_keys.py # 证书生成器
│   ├── generate_keys.bat       # 证书生成批处理
│   ├── custom_key.x509.pem     # 自定义证书（生成后）
│   ├── custom_key.pk8          # 自定义私钥（生成后）
│   └── current_signature.txt   # 当前签名信息（生成后）
├── update/                      # 解包目录（使用后生成）
├── update.zip                   # 原始固件（用户放置）
└── update_signed.zip           # 签名后固件（生成后）
```

## 注意事项

1. **证书安全**: 请妥善保管生成的证书和私钥文件
2. **固件兼容**: 确保修改的固件与目标设备兼容
3. **备份重要**: 刷机前请备份原始固件
4. **风险提示**: 刷机有风险，操作需谨慎

## 技术原理

### 签名算法
- 使用RSA-2048位密钥
- PKCS#8格式私钥
- X.509格式证书
- SHA-256哈希算法

### 签名格式
- v4格式签名块
- 64字节头部
- 自定义魔数标识
- 双数组结构验证

## 版权信息

**By.举个🌰 2025**

本工具基于开源组件开发，仅供学习和研究使用。

## 更新日志

### v1.0.0 (2025-01-XX)
- 初始版本发布
- 支持基本的解包、打包、签名功能
- 支持自定义签名生成
- 支持自定义证书生成

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址: [GitHub链接]
- 邮箱: [联系邮箱]

---
**免责声明**: 本工具仅供学习研究使用，使用者需自行承担使用风险。
