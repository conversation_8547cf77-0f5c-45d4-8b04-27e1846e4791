#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ROM自定义签名工具 GUI版本
By.举个🌰 2025
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import subprocess
import threading
import zipfile
import shutil
from pathlib import Path
import json
from datetime import datetime

class ROMSignatureTool:
    def __init__(self, root):
        self.root = root
        self.root.title("ROM自定义签名工具 - By.举个🌰 2025")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置工作目录
        self.work_dir = Path(__file__).parent
        self.bin_dir = self.work_dir / "bin"
        self.update_dir = self.work_dir / "update"
        
        # 创建界面
        self.create_widgets()
        
        # 检查必要文件
        self.check_required_files()
    
    def create_widgets(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="ROM自定义签名工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="ROM文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=50)
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)
        
        # 操作按钮区域
        button_frame = ttk.LabelFrame(main_frame, text="操作选择", padding="10")
        button_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮布局
        ttk.Button(button_frame, text="解包ROM", command=self.unpack_rom, 
                  width=15).grid(row=0, column=0, padx=5, pady=5)
        ttk.Button(button_frame, text="打包+签名", command=self.pack_and_sign, 
                  width=15).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(button_frame, text="生成自定义签名", command=self.generate_signature, 
                  width=15).grid(row=0, column=2, padx=5, pady=5)
        ttk.Button(button_frame, text="查看签名信息", command=self.view_signature, 
                  width=15).grid(row=0, column=3, padx=5, pady=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.grid(row=4, column=0, columnspan=3, sticky=tk.W)
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def update_status(self, status):
        """更新状态"""
        self.status_var.set(status)
        self.root.update()
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_var.set(value)
        self.root.update()
    
    def browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择ROM文件",
            filetypes=[("ZIP文件", "*.zip"), ("所有文件", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
    
    def check_required_files(self):
        """检查必要文件"""
        required_files = [
            self.bin_dir / "7za.exe",
            self.bin_dir / "signapk.jar",
            self.bin_dir / "custom_key.x509.pem",
            self.bin_dir / "custom_key.pk8"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not file_path.exists():
                missing_files.append(file_path.name)
        
        if missing_files:
            messagebox.showwarning("缺少文件", 
                                 f"缺少以下必要文件:\n{chr(10).join(missing_files)}")
            self.log_message(f"警告: 缺少必要文件 {', '.join(missing_files)}")
        else:
            self.log_message("所有必要文件检查完成")
    
    def run_command(self, cmd, cwd=None):
        """运行命令"""
        try:
            if cwd is None:
                cwd = self.work_dir
            
            self.log_message(f"执行命令: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
            
            result = subprocess.run(
                cmd, 
                cwd=cwd, 
                capture_output=True, 
                text=True, 
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.stdout:
                self.log_message(f"输出: {result.stdout.strip()}")
            if result.stderr:
                self.log_message(f"错误: {result.stderr.strip()}")
            
            return result.returncode == 0
        except Exception as e:
            self.log_message(f"命令执行失败: {str(e)}")
            return False

    def unpack_rom(self):
        """解包ROM"""
        def unpack_thread():
            try:
                rom_file = self.file_path_var.get()
                if not rom_file:
                    messagebox.showerror("错误", "请先选择ROM文件")
                    return

                if not os.path.exists(rom_file):
                    messagebox.showerror("错误", "ROM文件不存在")
                    return

                self.update_status("正在解包ROM...")
                self.update_progress(10)
                self.log_message("开始解包ROM文件")

                # 清理旧的解包目录
                if self.update_dir.exists():
                    shutil.rmtree(self.update_dir)
                    self.log_message("清理旧的解包目录")

                self.update_dir.mkdir(exist_ok=True)
                self.update_progress(30)

                # 复制ROM文件到工作目录
                work_rom = self.work_dir / "update.zip"
                shutil.copy2(rom_file, work_rom)
                self.log_message("复制ROM文件到工作目录")
                self.update_progress(50)

                # 使用7za解压
                cmd = [str(self.bin_dir / "7za.exe"), "x", "-y", str(work_rom)]
                if self.run_command(cmd, cwd=self.update_dir):
                    self.update_progress(100)
                    self.update_status("ROM解包完成")
                    self.log_message("ROM解包完成，文件保存在update目录")
                    messagebox.showinfo("成功", "ROM解包完成！\n解包文件保存在update目录中")
                else:
                    self.update_status("ROM解包失败")
                    messagebox.showerror("错误", "ROM解包失败，请查看日志")

                # 清理临时文件
                if work_rom.exists():
                    work_rom.unlink()

            except Exception as e:
                self.log_message(f"解包过程出错: {str(e)}")
                self.update_status("解包失败")
                messagebox.showerror("错误", f"解包过程出错: {str(e)}")
            finally:
                self.update_progress(0)

        # 在新线程中执行
        threading.Thread(target=unpack_thread, daemon=True).start()

    def pack_and_sign(self):
        """打包并签名"""
        def pack_sign_thread():
            try:
                if not self.update_dir.exists() or not any(self.update_dir.iterdir()):
                    messagebox.showerror("错误", "没有找到解包的文件，请先解包ROM")
                    return

                self.update_status("正在打包ROM...")
                self.update_progress(10)
                self.log_message("开始打包ROM文件")

                # 打包文件
                temp_zip = self.work_dir / "update_temp.zip"
                if temp_zip.exists():
                    temp_zip.unlink()

                cmd = [str(self.bin_dir / "7za.exe"), "a", "-y", str(temp_zip)]
                if self.run_command(cmd, cwd=self.update_dir):
                    self.update_progress(50)
                    self.log_message("ROM打包完成，开始签名")
                else:
                    self.update_status("打包失败")
                    messagebox.showerror("错误", "ROM打包失败")
                    return

                # 签名文件
                self.update_status("正在签名ROM...")
                signed_zip = self.work_dir / "update_signed.zip"
                if signed_zip.exists():
                    signed_zip.unlink()

                # 检查Java环境
                java_cmd = "java"
                try:
                    subprocess.run([java_cmd, "-version"], capture_output=True, check=True)
                except:
                    java_cmd = str(self.bin_dir / "java.exe")
                    if not Path(java_cmd).exists():
                        messagebox.showerror("错误", "未找到Java环境，请安装Java或将java.exe放入bin目录")
                        return

                cmd = [
                    java_cmd, "-Xmx2048m", "-jar", str(self.bin_dir / "signapk.jar"),
                    "-w", str(self.bin_dir / "custom_key.x509.pem"),
                    str(self.bin_dir / "custom_key.pk8"),
                    str(temp_zip), str(signed_zip)
                ]

                if self.run_command(cmd, cwd=self.bin_dir):
                    self.update_progress(100)
                    self.update_status("ROM签名完成")
                    self.log_message("ROM签名完成")

                    # 清理临时文件
                    if temp_zip.exists():
                        temp_zip.unlink()

                    messagebox.showinfo("成功", f"ROM打包签名完成！\n签名文件: {signed_zip.name}")
                else:
                    self.update_status("签名失败")
                    messagebox.showerror("错误", "ROM签名失败，请查看日志")

            except Exception as e:
                self.log_message(f"打包签名过程出错: {str(e)}")
                self.update_status("打包签名失败")
                messagebox.showerror("错误", f"打包签名过程出错: {str(e)}")
            finally:
                self.update_progress(0)

        # 在新线程中执行
        threading.Thread(target=pack_sign_thread, daemon=True).start()

    def generate_signature(self):
        """生成自定义签名"""
        try:
            # 导入签名生成器
            sys.path.append(str(self.bin_dir))
            from custom_signature_generator import CustomSignatureGenerator

            # 创建签名生成对话框
            self.create_signature_dialog()

        except ImportError:
            messagebox.showerror("错误", "未找到签名生成器模块")
            self.log_message("错误: 未找到custom_signature_generator.py")
        except Exception as e:
            messagebox.showerror("错误", f"生成签名时出错: {str(e)}")
            self.log_message(f"生成签名出错: {str(e)}")

    def create_signature_dialog(self):
        """创建签名生成对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("生成自定义签名")
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 150, self.root.winfo_rooty() + 100))

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="自定义签名生成器",
                 font=("Arial", 14, "bold")).pack(pady=(0, 20))

        # 魔数设置
        magic_frame = ttk.LabelFrame(main_frame, text="魔数设置", padding="10")
        magic_frame.pack(fill=tk.X, pady=(0, 10))

        self.use_custom_magic = tk.BooleanVar()
        ttk.Checkbutton(magic_frame, text="使用自定义魔数",
                       variable=self.use_custom_magic,
                       command=self.toggle_magic_entry).pack(anchor=tk.W)

        self.magic_var = tk.StringVar(value="0x3df72fd1")
        self.magic_entry = ttk.Entry(magic_frame, textvariable=self.magic_var, width=20)
        self.magic_entry.pack(anchor=tk.W, pady=(5, 0))
        self.magic_entry.config(state='disabled')

        # 数组大小设置
        size_frame = ttk.LabelFrame(main_frame, text="数组大小", padding="10")
        size_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(size_frame, text="签名数组大小:").pack(anchor=tk.W)
        self.array_size_var = tk.StringVar(value="64")
        size_spinbox = ttk.Spinbox(size_frame, from_=32, to=128,
                                  textvariable=self.array_size_var, width=10)
        size_spinbox.pack(anchor=tk.W, pady=(5, 0))

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Button(button_frame, text="生成签名",
                  command=lambda: self.do_generate_signature(dialog)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消",
                  command=dialog.destroy).pack(side=tk.LEFT)

        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="生成结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        self.signature_text = scrolledtext.ScrolledText(result_frame, height=8, width=60)
        self.signature_text.pack(fill=tk.BOTH, expand=True)

    def toggle_magic_entry(self):
        """切换魔数输入框状态"""
        if self.use_custom_magic.get():
            self.magic_entry.config(state='normal')
        else:
            self.magic_entry.config(state='disabled')

    def do_generate_signature(self, dialog):
        """执行签名生成"""
        try:
            sys.path.append(str(self.bin_dir))
            from custom_signature_generator import CustomSignatureGenerator

            generator = CustomSignatureGenerator()

            # 设置魔数
            if self.use_custom_magic.get():
                magic_str = self.magic_var.get().strip()
                if magic_str.startswith('0x'):
                    magic = int(magic_str, 16)
                else:
                    magic = int(magic_str)
                generator.set_custom_magic(magic)
            else:
                generator.set_custom_magic()

            # 设置数组大小
            array_size = int(self.array_size_var.get())
            generator.generate_signature_arrays(array_size, array_size)

            # 生成签名
            signature_str = generator.format_signature_output()

            # 保存到文件
            generator.save_signature_to_file(str(self.bin_dir / "current_signature.txt"))

            # 显示结果
            self.signature_text.delete(1.0, tk.END)
            self.signature_text.insert(1.0, signature_str)

            self.log_message("自定义签名生成完成")
            messagebox.showinfo("成功", "自定义签名生成完成！")

        except Exception as e:
            messagebox.showerror("错误", f"生成签名失败: {str(e)}")
            self.log_message(f"生成签名失败: {str(e)}")

    def view_signature(self):
        """查看签名信息"""
        signature_file = self.bin_dir / "current_signature.txt"
        if not signature_file.exists():
            messagebox.showwarning("提示", "未找到签名信息文件，请先生成自定义签名")
            return

        try:
            with open(signature_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 创建查看对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("签名信息")
            dialog.geometry("700x500")
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

            main_frame = ttk.Frame(dialog, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(main_frame, text="当前签名信息",
                     font=("Arial", 14, "bold")).pack(pady=(0, 10))

            text_widget = scrolledtext.ScrolledText(main_frame, height=20, width=80)
            text_widget.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            text_widget.insert(1.0, content)
            text_widget.config(state='disabled')

            ttk.Button(main_frame, text="关闭", command=dialog.destroy).pack()

        except Exception as e:
            messagebox.showerror("错误", f"读取签名信息失败: {str(e)}")
            self.log_message(f"读取签名信息失败: {str(e)}")


def main():
    """主函数"""
    root = tk.Tk()
    app = ROMSignatureTool(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 启动GUI
    root.mainloop()


if __name__ == "__main__":
    main()
