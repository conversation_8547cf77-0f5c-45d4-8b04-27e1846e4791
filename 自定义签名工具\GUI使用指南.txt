================================================================
ROM自定义签名工具 GUI版本 - 使用指南
By.举个🌰 2025
================================================================

一、快速开始
============
1. 双击运行 "启动工具.bat" 启动GUI界面
2. 点击"浏览"选择ROM文件（.zip格式）
3. 点击"解包ROM"解压固件
4. 修改解包后的文件（在update目录中）
5. 点击"打包+签名"重新打包并签名
6. 完成！签名后的文件为 update_signed.zip

二、界面说明
============

【文件选择区域】
- ROM文件：选择要处理的ROM文件
- 浏览按钮：打开文件选择对话框

【操作按钮】
- 解包ROM：解压ROM文件到update目录
- 打包+签名：将update目录打包并签名
- 生成自定义签名：生成v4格式的自定义签名
- 查看签名信息：查看当前生成的签名详情

【进度条】
显示当前操作的进度

【状态栏】
显示当前操作状态

【操作日志】
显示详细的操作过程和结果

三、详细操作步骤
===============

步骤1：解包ROM
--------------
1. 点击"浏览"选择ROM文件
2. 点击"解包ROM"按钮
3. 等待解包完成
4. 解包后的文件保存在"update"目录中

步骤2：修改固件（可选）
--------------------
1. 打开"update"目录
2. 修改需要的文件
3. 可以替换APK、修改配置文件等

步骤3：生成自定义签名
------------------
1. 点击"生成自定义签名"按钮
2. 在弹出对话框中设置：
   - 是否使用自定义魔数
   - 签名数组大小（默认64）
3. 点击"生成签名"
4. 签名信息保存在bin/current_signature.txt

步骤4：打包签名
--------------
1. 点击"打包+签名"按钮
2. 等待打包和签名完成
3. 生成的文件为"update_signed.zip"

四、注意事项
============
1. 需要Python 3.6+环境
2. 确保bin目录下有必要的工具文件
3. 签名过程需要Java环境
4. 修改固件时请备份原文件
5. 刷机有风险，请谨慎操作

五、故障排除
============

问题1：Python未找到
解决：安装Python 3.6+或使用py命令

问题2：Java未找到
解决：安装Java或将java.exe放入bin目录

问题3：缺少工具文件
解决：确保bin目录包含所有必要文件

问题4：签名失败
解决：检查证书文件是否存在和有效

六、文件说明
============
- ROM_Signature_Tool.py：主程序
- 启动工具.bat：启动脚本
- bin/：工具目录
  - 7za.exe：压缩工具
  - signapk.jar：签名工具
  - custom_key.x509.pem：证书文件
  - custom_key.pk8：私钥文件
  - custom_signature_generator.py：签名生成器
- update/：解包目录（使用后生成）
- update_signed.zip：签名后文件（生成后）

================================================================
版权信息：By.举个🌰 2025
================================================================
