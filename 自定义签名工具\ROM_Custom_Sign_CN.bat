@echo off
color 0a
title ROM自定义签名工具 - By.举个🌰 2025
:menu
cls
echo ################################################################
echo # ROM自定义签名工具 - By.举个🌰 2025                           #
echo # 请选择您需要的操作                                           #
echo # 1 -- 解包      (解压固件包即可手动进行修改)                  #
echo # 2 -- 打包+签名 (手动修改固件后重新打包签名)                  #
echo # 3 -- 生成自定义签名                                          #
echo # 4 -- 查看当前签名信息                                        #
echo # 5 -- 退出                                                    #
echo ################################################################
:cho
set choice=
set /p choice= 请输入您选择的操作(数字):
if /i "%choice%"=="1" goto unpack
if /i "%choice%"=="2" goto pack
if /i "%choice%"=="3" goto generate_signature
if /i "%choice%"=="4" goto view_signature
if /i "%choice%"=="5" goto end
goto menu

:unpack
if not exist .\update.zip goto nofile
echo.
echo ###################################
echo #   开始解包固件，请稍等 ...      #
echo ###################################
echo.
if exist .\update rd /S /Q .\update
md .\update
cd .\update
..\bin\7za.exe x -y ..\update.zip
cd ..
echo.
echo ########################################################
echo #   固件解包完成 ...                                   #
echo #   生成的update目录即为已解包固件，手动去修改吧！     #
echo ########################################################
echo.
pause
goto menu

:pack
if not exist .\update goto nodate
echo.
echo ####################################
echo #   开始打包+签名，请稍等 ...      #
echo ####################################
echo.
cd .\update
..\bin\7za.exe a -y ..\update_diy.zip
cd ..
echo.
echo 开始签名 ...
cd .\bin
java.exe -Xmx2048m -jar signapk.jar -w custom_key.x509.pem custom_key.pk8 ..\update_diy.zip ..\update_signed.zip
del ..\update_diy.zip
cd ..
echo 签名完成 ...
echo.
echo #########################################################
echo #   固件打包+签名完成 ...                               #
echo #   生成的update_signed.zip即为update.zip，去刷机吧！   #
echo #########################################################
echo.
pause
goto menu

:generate_signature
echo.
echo ####################################
echo #   生成自定义签名 ...             #
echo ####################################
echo.
cd .\bin
python custom_signature_generator.py
cd ..
echo.
echo 自定义签名生成完成！
pause
goto menu

:view_signature
echo.
echo ####################################
echo #   查看当前签名信息 ...           #
echo ####################################
echo.
if exist .\bin\current_signature.txt (
    type .\bin\current_signature.txt
) else (
    echo 未找到签名信息文件，请先生成自定义签名！
)
echo.
pause
goto menu

:nofile
echo.
echo #########################################################
echo #   目录下没有update.zip固件包                          #
echo #   请去下载相应固件包并重命名为update.zip放在此目录下    #
echo #########################################################
echo.
pause
goto menu

:nodate
echo.
echo #########################################
echo #   没有解包的固件，请先选择解包！    #
echo #########################################
echo.
pause
goto menu

:end
exit
